'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { User, LogIn } from 'lucide-react';

export const LoginTest: React.FC = () => {
  const { login, user, isAuthenticated, loading } = useAuth();
  const router = useRouter();
  const [credentials, setCredentials] = useState({
    email: '<EMAIL>',
    password: 'TestPass123'
  });
  const [isLoggingIn, setIsLoggingIn] = useState(false);

  const handleLogin = async () => {
    try {
      setIsLoggingIn(true);
      console.log('Starting login with:', credentials);

      const success = await login(credentials.email, credentials.password, () => {
        console.log('Login callback triggered, redirecting...');
        router.push('/dashboard');
      });

      console.log('Login result:', success);
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsLoggingIn(false);
    }
  };

  const handleDirectRedirect = () => {
    console.log('Direct redirect to dashboard');
    router.push('/dashboard');
  };

  return (
    <div className="card max-w-md mx-auto">
      <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
        <LogIn className="w-6 h-6 text-green-600" />
        <span>Login Test</span>
      </h2>

      {/* Current Auth State */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-2">Current Auth State</h3>
        <div className="text-sm space-y-1">
          <p><strong>User:</strong> {user ? user.name : 'None'}</p>
          <p><strong>Email:</strong> {user ? user.email : 'None'}</p>
          <p><strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
          <p><strong>Loading:</strong> {loading.isLoading ? 'Yes' : 'No'}</p>
          <p><strong>Token:</strong> {typeof window !== 'undefined' && localStorage.getItem('authToken') ? 'Present' : 'None'}</p>
        </div>
      </div>

      {/* Login Form */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input
            type="email"
            value={credentials.email}
            onChange={(e) => setCredentials(prev => ({ ...prev, email: e.target.value }))}
            className="input-field"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Password</label>
          <input
            type="password"
            value={credentials.password}
            onChange={(e) => setCredentials(prev => ({ ...prev, password: e.target.value }))}
            className="input-field"
          />
        </div>

        <div className="space-y-2">
          <LoadingButton
            onClick={handleLogin}
            isLoading={isLoggingIn}
            className="w-full"
          >
            Test Login
          </LoadingButton>
          
          <button
            onClick={handleDirectRedirect}
            className="w-full btn-secondary"
          >
            Direct Redirect to Dashboard
          </button>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Test Instructions</h4>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>First register a user with the test credentials above</li>
          <li>Verify the email with OTP</li>
          <li>Then use this login test</li>
          <li>Check the console for debug information</li>
        </ol>
      </div>
    </div>
  );
};
