// User types
export interface User {
  id: string;
  name: string;
  email: string;
  isEmailVerified: boolean;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
}

// Auth types
export interface RegisterData {
  name: string;
  email: string;
  password: string;
  gender: 'male' | 'female' | 'other' | 'prefer_not_to_say';
}

export interface LoginData {
  email: string;
  password: string;
}

export interface OTPData {
  email: string;
  otp: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  errors?: Array<{
    msg: string;
    param: string;
  }>;
  retryAfter?: number;
}

export interface AuthResponse {
  token: string;
  user: User;
}

// Face Analysis types
export interface FaceAnalysis {
  _id: string;
  imageUrl: string;
  publicId: string;
  faceDetected: boolean;
  colors: {
    dominantColors: string[];
    skinTone: string;
    eyeColor: string;
    hairColor: string;
  };
  facialFeatures: {
    faceShape: string;
    eyeShape: string;
  };
  confidence: number;
  createdAt: string;
}

export interface AnalysisRequest {
  imageUrl: string;
  publicId: string;
}

// Color Recommendation types
export interface OutfitRecommendation {
  outfitName: string;
  shirt: {
    color: string;
    hex: string;
    reason: string;
  };
  pants: {
    color: string;
    hex: string;
    reason: string;
  };
}

export interface ColorRecommendation {
  recommendations: OutfitRecommendation[];
  colorPalette: {
    bestColors: string[];
    avoidColors: string[];
    seasonalType: string;
  };
  generalAdvice: string;
}

export interface RecommendationRequest {
  preferences: {
    style?: string;
    occasion?: string;
  };
}

// Upload types
export interface UploadSignature {
  signature: string;
  timestamp: number;
  cloudName: string;
  apiKey: string;
  uploadUrl: string;
  folder: string;
}

export interface CloudinaryUploadResult {
  secure_url: string;
  public_id: string;
  width: number;
  height: number;
  format: string;
  resource_type: string;
}

// Feedback types
export interface FeedbackData {
  rating: number;
  feedback: string;
  favoriteOutfits: number[];
}

// Loading states
export interface LoadingState {
  isLoading: boolean;
  message?: string;
}

// Error types
export interface ErrorState {
  hasError: boolean;
  message?: string;
  code?: string;
}
