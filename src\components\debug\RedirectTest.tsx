'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { ArrowRight, Home, User } from 'lucide-react';

export const RedirectTest: React.FC = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();

  const testRedirects = [
    {
      name: 'Dashboard',
      path: '/dashboard',
      icon: <Home className="w-4 h-4" />,
      description: 'Go to main dashboard'
    },
    {
      name: 'Auth Page',
      path: '/auth',
      icon: <User className="w-4 h-4" />,
      description: 'Go to authentication page'
    },
    {
      name: 'Home',
      path: '/',
      icon: <Home className="w-4 h-4" />,
      description: 'Go to home page'
    }
  ];

  const handleRedirect = (path: string) => {
    console.log(`Redirecting to: ${path}`);
    router.push(path);
  };

  const handleReplace = (path: string) => {
    console.log(`Replacing with: ${path}`);
    router.replace(path);
  };

  const forceReload = () => {
    console.log('Force reloading page');
    window.location.reload();
  };

  const forceNavigate = (path: string) => {
    console.log(`Force navigating to: ${path}`);
    window.location.href = path;
  };

  return (
    <div className="card max-w-lg mx-auto">
      <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
        <ArrowRight className="w-6 h-6 text-blue-600" />
        <span>Navigation Test</span>
      </h2>

      {/* Current State */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-2">Current State</h3>
        <div className="text-sm space-y-1">
          <p><strong>User:</strong> {user?.name || 'Not logged in'}</p>
          <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
          <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.pathname : 'Unknown'}</p>
        </div>
      </div>

      {/* Router.push Tests */}
      <div className="space-y-4">
        <h3 className="font-medium text-gray-900">Router.push() Tests</h3>
        <div className="grid gap-2">
          {testRedirects.map((redirect) => (
            <button
              key={redirect.path}
              onClick={() => handleRedirect(redirect.path)}
              className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {redirect.icon}
                <div className="text-left">
                  <p className="font-medium text-gray-900">{redirect.name}</p>
                  <p className="text-sm text-gray-600">{redirect.description}</p>
                </div>
              </div>
              <ArrowRight className="w-4 h-4 text-gray-400" />
            </button>
          ))}
        </div>
      </div>

      {/* Router.replace Tests */}
      <div className="space-y-4 mt-6">
        <h3 className="font-medium text-gray-900">Router.replace() Tests</h3>
        <div className="grid gap-2">
          {testRedirects.map((redirect) => (
            <button
              key={`replace-${redirect.path}`}
              onClick={() => handleReplace(redirect.path)}
              className="flex items-center justify-between p-3 border border-blue-200 rounded-lg hover:bg-blue-50 transition-colors"
            >
              <div className="flex items-center space-x-3">
                {redirect.icon}
                <div className="text-left">
                  <p className="font-medium text-blue-900">Replace → {redirect.name}</p>
                  <p className="text-sm text-blue-600">{redirect.description}</p>
                </div>
              </div>
              <ArrowRight className="w-4 h-4 text-blue-400" />
            </button>
          ))}
        </div>
      </div>

      {/* Force Navigation */}
      <div className="space-y-4 mt-6">
        <h3 className="font-medium text-gray-900">Force Navigation</h3>
        <div className="grid gap-2">
          <button
            onClick={forceReload}
            className="p-3 border border-yellow-200 rounded-lg hover:bg-yellow-50 transition-colors text-left"
          >
            <p className="font-medium text-yellow-900">Force Reload</p>
            <p className="text-sm text-yellow-600">Reload current page</p>
          </button>
          
          <button
            onClick={() => forceNavigate('/dashboard')}
            className="p-3 border border-green-200 rounded-lg hover:bg-green-50 transition-colors text-left"
          >
            <p className="font-medium text-green-900">Force Navigate to Dashboard</p>
            <p className="text-sm text-green-600">Use window.location.href</p>
          </button>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">Test Instructions</h4>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>Try different navigation methods</li>
          <li>Check browser console for logs</li>
          <li>Observe which method works for redirects</li>
          <li>Test both authenticated and non-authenticated states</li>
        </ol>
      </div>
    </div>
  );
};
