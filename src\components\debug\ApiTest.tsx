'use client';

import React, { useState } from 'react';
import { healthAPI } from '@/lib/api';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import { CheckCircle, XCircle, Activity } from 'lucide-react';
import toast from 'react-hot-toast';

export const ApiTest: React.FC = () => {
  const [isTestingHealth, setIsTestingHealth] = useState(false);
  const [isTestingPing, setIsTestingPing] = useState(false);
  const [healthResult, setHealthResult] = useState<any>(null);
  const [pingResult, setPingResult] = useState<string | null>(null);

  const testHealthCheck = async () => {
    try {
      setIsTestingHealth(true);
      const result = await healthAPI.check();
      setHealthResult(result);
      toast.success('Health check successful!');
    } catch (error: any) {
      console.error('Health check failed:', error);
      setHealthResult({ success: false, error: error.message });
      toast.error('Health check failed');
    } finally {
      setIsTestingHealth(false);
    }
  };

  const testPing = async () => {
    try {
      setIsTestingPing(true);
      const result = await healthAPI.ping();
      setPingResult(result);
      toast.success('Ping successful!');
    } catch (error: any) {
      console.error('Ping failed:', error);
      setPingResult(`Error: ${error.message}`);
      toast.error('Ping failed');
    } finally {
      setIsTestingPing(false);
    }
  };

  return (
    <div className="card max-w-2xl mx-auto">
      <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
        <Activity className="w-6 h-6 text-blue-600" />
        <span>API Connection Test</span>
      </h2>

      <div className="space-y-6">
        {/* Health Check */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Health Check</h3>
            <LoadingButton
              onClick={testHealthCheck}
              isLoading={isTestingHealth}
              className="px-4 py-2"
            >
              Test Health
            </LoadingButton>
          </div>
          
          {healthResult && (
            <div className={`p-4 rounded-lg border ${
              healthResult.success 
                ? 'bg-green-50 border-green-200' 
                : 'bg-red-50 border-red-200'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                {healthResult.success ? (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                ) : (
                  <XCircle className="w-5 h-5 text-red-600" />
                )}
                <span className={`font-medium ${
                  healthResult.success ? 'text-green-900' : 'text-red-900'
                }`}>
                  {healthResult.success ? 'API is healthy' : 'API check failed'}
                </span>
              </div>
              <pre className="text-sm text-gray-700 bg-white p-2 rounded border overflow-auto">
                {JSON.stringify(healthResult, null, 2)}
              </pre>
            </div>
          )}
        </div>

        {/* Ping Test */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-medium text-gray-900">Ping Test</h3>
            <LoadingButton
              onClick={testPing}
              isLoading={isTestingPing}
              className="px-4 py-2"
            >
              Test Ping
            </LoadingButton>
          </div>
          
          {pingResult && (
            <div className={`p-4 rounded-lg border ${
              pingResult.includes('Error') 
                ? 'bg-red-50 border-red-200' 
                : 'bg-green-50 border-green-200'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                {pingResult.includes('Error') ? (
                  <XCircle className="w-5 h-5 text-red-600" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-600" />
                )}
                <span className={`font-medium ${
                  pingResult.includes('Error') ? 'text-red-900' : 'text-green-900'
                }`}>
                  Ping Result
                </span>
              </div>
              <pre className="text-sm text-gray-700 bg-white p-2 rounded border">
                {pingResult}
              </pre>
            </div>
          )}
        </div>

        {/* API Info */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">API Configuration</h4>
          <div className="text-sm text-blue-800 space-y-1">
            <p><strong>Base URL:</strong> https://faceapp-ttwh.onrender.com/api</p>
            <p><strong>Health Endpoint:</strong> /health</p>
            <p><strong>Ping Endpoint:</strong> /ping</p>
          </div>
        </div>
      </div>
    </div>
  );
};
