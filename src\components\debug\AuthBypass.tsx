'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { Shield, Key, User } from 'lucide-react';

export const AuthBypass: React.FC = () => {
  const router = useRouter();

  const setTestToken = () => {
    // Set a test token (this is the real token from our API test)
    const testToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.AXSZBpRHSN-0Atw49nZBfc4BvHXl0xirLYefcfybuJY';
    
    localStorage.setItem('authToken', testToken);
    console.log('Test token set in localStorage');
    
    // Force reload to trigger auth check
    window.location.reload();
  };

  const clearToken = () => {
    localStorage.removeItem('authToken');
    console.log('Token cleared from localStorage');
    window.location.reload();
  };

  const goToDashboard = () => {
    router.push('/dashboard');
  };

  const goToDashboardTest = () => {
    router.push('/dashboard-test');
  };

  const currentToken = typeof window !== 'undefined' ? localStorage.getItem('authToken') : null;

  return (
    <div className="card max-w-md mx-auto">
      <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center space-x-2">
        <Shield className="w-6 h-6 text-orange-600" />
        <span>Auth Bypass (Testing)</span>
      </h2>

      {/* Current Token Status */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="font-medium text-gray-900 mb-2">Current Token Status</h3>
        <div className="text-sm">
          <p><strong>Token Present:</strong> {currentToken ? 'Yes' : 'No'}</p>
          {currentToken && (
            <p className="break-all mt-2">
              <strong>Token:</strong> {currentToken.substring(0, 50)}...
            </p>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="space-y-3">
        <button
          onClick={setTestToken}
          className="w-full flex items-center justify-center space-x-2 p-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
        >
          <Key className="w-4 h-4" />
          <span>Set Test Token</span>
        </button>

        <button
          onClick={clearToken}
          className="w-full flex items-center justify-center space-x-2 p-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
        >
          <User className="w-4 h-4" />
          <span>Clear Token</span>
        </button>

        <button
          onClick={goToDashboard}
          className="w-full btn-primary"
        >
          Go to Dashboard
        </button>

        <button
          onClick={goToDashboardTest}
          className="w-full btn-secondary"
        >
          Go to Dashboard Test
        </button>
      </div>

      {/* Warning */}
      <div className="mt-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
        <h4 className="font-medium text-orange-900 mb-2">⚠️ Testing Only</h4>
        <p className="text-sm text-orange-800">
          This component is for testing authentication flow. The test token is valid for the registered test user.
        </p>
      </div>

      {/* Instructions */}
      <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-900 mb-2">How to Test</h4>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>Click "Set Test Token" to simulate login</li>
          <li>Try "Go to Dashboard" to test redirect</li>
          <li>Check browser console for debug info</li>
          <li>Use "Clear Token" to test logout</li>
        </ol>
      </div>
    </div>
  );
};
