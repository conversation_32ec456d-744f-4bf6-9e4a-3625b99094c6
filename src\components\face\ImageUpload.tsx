'use client';

import React, { useState, useRef } from 'react';
import { Upload, Camera, X, Image as ImageIcon } from 'lucide-react';
import { validateImageFile, uploadImageToCloudinary } from '@/lib/utils';
import { LoadingButton } from '@/components/ui/LoadingSpinner';
import toast from 'react-hot-toast';

interface ImageUploadProps {
  onUploadSuccess: (imageUrl: string, publicId: string) => void;
  isAnalyzing?: boolean;
}

export const ImageUpload: React.FC<ImageUploadProps> = ({ 
  onUploadSuccess, 
  isAnalyzing = false 
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      toast.error(validation.error!);
      return;
    }

    setSelectedFile(file);
    const url = URL.createObjectURL(file);
    setPreviewUrl(url);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const clearSelection = () => {
    setSelectedFile(null);
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      setIsUploading(true);
      const result = await uploadImageToCloudinary(selectedFile);
      onUploadSuccess(result.secure_url, result.public_id);
      toast.success('Image uploaded successfully!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to upload image');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-4">
      {!previewUrl ? (
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors duration-200 cursor-pointer"
          onClick={() => fileInputRef.current?.click()}
        >
          <div className="space-y-4">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
              <Upload className="w-8 h-8 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Upload Your Photo
              </h3>
              <p className="text-gray-600 mb-4">
                Drag and drop your image here, or click to browse
              </p>
              <div className="flex items-center justify-center space-x-4">
                <button
                  type="button"
                  className="btn-primary flex items-center space-x-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    fileInputRef.current?.click();
                  }}
                >
                  <ImageIcon className="w-4 h-4" />
                  <span>Choose File</span>
                </button>
                <button
                  type="button"
                  className="btn-secondary flex items-center space-x-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    // TODO: Implement camera capture
                    toast.info('Camera feature coming soon!');
                  }}
                >
                  <Camera className="w-4 h-4" />
                  <span>Take Photo</span>
                </button>
              </div>
            </div>
            <p className="text-sm text-gray-500">
              Supports JPEG, PNG, WebP (max 10MB)
            </p>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="relative">
            <img
              src={previewUrl}
              alt="Preview"
              className="w-full max-w-md mx-auto rounded-lg shadow-lg"
            />
            <button
              onClick={clearSelection}
              className="absolute top-2 right-2 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 transition-colors duration-200"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
          
          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              {selectedFile?.name} ({(selectedFile?.size || 0 / 1024 / 1024).toFixed(2)} MB)
            </p>
            <div className="flex justify-center space-x-3">
              <LoadingButton
                onClick={handleUpload}
                isLoading={isUploading || isAnalyzing}
                disabled={!selectedFile}
                className="px-6"
              >
                {isAnalyzing ? 'Analyzing...' : 'Analyze Face'}
              </LoadingButton>
              <button
                onClick={clearSelection}
                className="btn-secondary px-6"
                disabled={isUploading || isAnalyzing}
              >
                Choose Different
              </button>
            </div>
          </div>
        </div>
      )}

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Tips */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Tips for best results:</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Use a clear, well-lit photo of your face</li>
          <li>• Face the camera directly with minimal makeup</li>
          <li>• Avoid shadows or harsh lighting</li>
          <li>• Ensure your face takes up most of the frame</li>
        </ul>
      </div>
    </div>
  );
};
