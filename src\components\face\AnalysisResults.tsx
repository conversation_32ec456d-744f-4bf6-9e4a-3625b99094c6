'use client';

import React from 'react';
import { FaceAnalysis } from '@/types';
import { formatConfidence, formatDate, getContrastColor } from '@/lib/utils';
import { Eye, Palette, User, Calendar, TrendingUp } from 'lucide-react';

interface AnalysisResultsProps {
  analysis: FaceAnalysis;
  onGetRecommendations?: () => void;
  isLoadingRecommendations?: boolean;
}

export const AnalysisResults: React.FC<AnalysisResultsProps> = ({
  analysis,
  onGetRecommendations,
  isLoadingRecommendations = false,
}) => {
  const { colors, facialFeatures, confidence, imageUrl, createdAt } = analysis;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Analysis Complete!</h2>
        <p className="text-gray-600">Here's what we discovered about your unique features</p>
      </div>

      {/* Image and Basic Info */}
      <div className="card">
        <div className="flex flex-col md:flex-row gap-6">
          <div className="md:w-1/3">
            <img
              src={imageUrl}
              alt="Analyzed face"
              className="w-full rounded-lg shadow-md"
            />
          </div>
          <div className="md:w-2/3 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Analysis Summary</h3>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <span className="text-green-600 font-medium">
                  {formatConfidence(confidence)} confidence
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="flex items-center space-x-3">
                <User className="w-5 h-5 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600">Face Shape</p>
                  <p className="font-medium capitalize">{facialFeatures.faceShape}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Eye className="w-5 h-5 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600">Eye Shape</p>
                  <p className="font-medium capitalize">{facialFeatures.eyeShape}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Calendar className="w-5 h-5 text-gray-600" />
                <div>
                  <p className="text-sm text-gray-600">Analyzed</p>
                  <p className="font-medium">{formatDate(createdAt)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Color Analysis */}
      <div className="card">
        <div className="flex items-center space-x-2 mb-4">
          <Palette className="w-6 h-6 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-900">Color Analysis</h3>
        </div>
        
        <div className="space-y-6">
          {/* Skin Tone */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Skin Tone</h4>
            <div className="flex items-center space-x-4">
              <div
                className="w-16 h-16 rounded-full border-2 border-gray-200 shadow-sm"
                style={{ backgroundColor: colors.skinTone }}
              />
              <div>
                <p className="font-medium" style={{ color: colors.skinTone }}>
                  {colors.skinTone}
                </p>
                <p className="text-sm text-gray-600">Primary skin tone</p>
              </div>
            </div>
          </div>

          {/* Eye Color */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Eye Color</h4>
            <div className="flex items-center space-x-4">
              <div
                className="w-16 h-16 rounded-full border-2 border-gray-200 shadow-sm"
                style={{ backgroundColor: colors.eyeColor }}
              />
              <div>
                <p className="font-medium" style={{ color: colors.eyeColor }}>
                  {colors.eyeColor}
                </p>
                <p className="text-sm text-gray-600">Dominant eye color</p>
              </div>
            </div>
          </div>

          {/* Hair Color */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Hair Color</h4>
            <div className="flex items-center space-x-4">
              <div
                className="w-16 h-16 rounded-full border-2 border-gray-200 shadow-sm"
                style={{ backgroundColor: colors.hairColor }}
              />
              <div>
                <p className="font-medium" style={{ color: colors.hairColor }}>
                  {colors.hairColor}
                </p>
                <p className="text-sm text-gray-600">Natural hair color</p>
              </div>
            </div>
          </div>

          {/* Dominant Colors */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Dominant Colors</h4>
            <div className="flex flex-wrap gap-3">
              {colors.dominantColors.map((color, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div
                    className="w-8 h-8 rounded-full border border-gray-200 shadow-sm"
                    style={{ backgroundColor: color }}
                  />
                  <span
                    className="text-sm font-medium"
                    style={{ color: color }}
                  >
                    {color}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Get Recommendations Button */}
      {onGetRecommendations && (
        <div className="text-center">
          <button
            onClick={onGetRecommendations}
            disabled={isLoadingRecommendations}
            className="btn-primary px-8 py-3 text-lg"
          >
            {isLoadingRecommendations ? 'Getting Recommendations...' : 'Get Color Recommendations'}
          </button>
          <p className="text-sm text-gray-600 mt-2">
            Discover outfits and colors that complement your unique features
          </p>
        </div>
      )}
    </div>
  );
};
