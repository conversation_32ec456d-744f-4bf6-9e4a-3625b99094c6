'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { LoadingOverlay } from '@/components/ui/LoadingSpinner';

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, loading } = useAuth();

  useEffect(() => {
    if (!loading.isLoading) {
      if (isAuthenticated) {
        console.log('Authenticated user, redirecting to dashboard');
        router.replace('/dashboard');
      } else {
        console.log('Not authenticated, redirecting to auth');
        router.replace('/auth');
      }
    }
  }, [isAuthenticated, loading.isLoading, router]);

  return <LoadingOverlay message="Loading Face App..." />;
}
