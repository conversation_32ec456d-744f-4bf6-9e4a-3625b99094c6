'use client';

import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { CheckCircle, XCircle, User, ArrowLeft } from 'lucide-react';

export default function DashboardTestPage() {
  const { user, isAuthenticated, loading } = useAuth();
  const router = useRouter();

  const goBack = () => {
    router.push('/auth');
  };

  const goToDashboard = () => {
    router.push('/dashboard');
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="card max-w-md w-full">
        <h1 className="text-2xl font-bold text-gray-900 mb-6 text-center">
          Dashboard Access Test
        </h1>

        {/* Loading State */}
        {loading.isLoading && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Checking authentication...</p>
          </div>
        )}

        {/* Authentication Status */}
        {!loading.isLoading && (
          <div className="space-y-6">
            <div className="flex items-center justify-center space-x-3">
              {isAuthenticated ? (
                <CheckCircle className="w-8 h-8 text-green-600" />
              ) : (
                <XCircle className="w-8 h-8 text-red-600" />
              )}
              <span className={`text-lg font-medium ${
                isAuthenticated ? 'text-green-900' : 'text-red-900'
              }`}>
                {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
              </span>
            </div>

            {/* User Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>User Information</span>
              </h3>
              <div className="space-y-2 text-sm">
                <p><strong>Name:</strong> {user?.name || 'None'}</p>
                <p><strong>Email:</strong> {user?.email || 'None'}</p>
                <p><strong>Email Verified:</strong> {user?.isEmailVerified ? 'Yes' : 'No'}</p>
                <p><strong>Gender:</strong> {user?.gender || 'Not specified'}</p>
              </div>
            </div>

            {/* Token Info */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-3">Token Status</h3>
              <div className="space-y-2 text-sm">
                <p><strong>Token Present:</strong> {
                  typeof window !== 'undefined' && localStorage.getItem('authToken') 
                    ? 'Yes' 
                    : 'No'
                }</p>
                {typeof window !== 'undefined' && localStorage.getItem('authToken') && (
                  <p className="break-all"><strong>Token:</strong> {localStorage.getItem('authToken')?.substring(0, 50)}...</p>
                )}
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              {isAuthenticated ? (
                <button
                  onClick={goToDashboard}
                  className="w-full btn-primary"
                >
                  Go to Dashboard
                </button>
              ) : (
                <div className="text-center">
                  <p className="text-red-600 mb-4">
                    You need to be authenticated to access the dashboard.
                  </p>
                  <button
                    onClick={goBack}
                    className="btn-secondary flex items-center space-x-2 mx-auto"
                  >
                    <ArrowLeft className="w-4 h-4" />
                    <span>Go to Login</span>
                  </button>
                </div>
              )}
            </div>

            {/* Debug Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Debug Information</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <p><strong>Current URL:</strong> {typeof window !== 'undefined' ? window.location.href : 'Unknown'}</p>
                <p><strong>Loading:</strong> {loading.isLoading ? 'Yes' : 'No'}</p>
                <p><strong>Auth State:</strong> {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
